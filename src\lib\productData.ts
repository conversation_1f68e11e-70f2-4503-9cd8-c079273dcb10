// Define Product interface
export interface Product {
  id: number;
  title: string;
  description: string;
  stock: number;
  price: number;
  isHot: boolean;
  category: string;
}

// Mock Data for products
export const products: Product[] = [
  {
    id: 1,
    title: 'Full info with Sim-Swap (CS-800+) BG+CR',
    description: '<PERSON>rem ipsum, dolor sit amet consectetur a',
    stock: 5,
    price: 45.00,
    isHot: true,
    category: 'Electronics',
  },
  {
    id: 2,
    title: 'Advanced User Data (AU-550) Full Access',
    description: 'Praesentium, similique, saepe, voluptates',
    stock: 8,
    price: 75.50,
    isHot: true,
    category: 'Electronics',
  },
  {
    id: 3,
    title: 'Designer Silk Scarf',
    description: 'Consectetur adipisicing elit. Quas, quod.',
    stock: 12,
    price: 25.00,
    isHot: false,
    category: 'Fashion',
  },
  {
    id: 4,
    title: 'Corporate Data Bundle (CD-1000) Pro',
    description: 'Eligendi, quam. Animi, quae, quos.',
    stock: 3,
    price: 120.00,
    isHot: true,
    category: 'Books',
  },
  {
    id: 5,
    title: 'Organic Green Tea Extract',
    description: 'A<PERSON>iam, at, corporis, cumque, debitis.',
    stock: 20,
    price: 35.75,
    isHot: false,
    category: 'Beauty & Health',
  },
]; 