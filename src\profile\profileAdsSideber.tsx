import type { FC } from "react";

interface ProfileAdsSidebarProps {
  className?: string;
}

const ProfileAdsSidebar: FC<ProfileAdsSidebarProps> = ({ className = "" }) => {
  return (
    <aside
      className={`h-fit w-72 p-6 rounded-2xl shadow-2xl ${className}`}
      style={{ backgroundColor: '#101828' }}
      aria-label="Advertisement display"
    >
      {/* Ad Slots */}
      <div className="space-y-6">
        {/* First Ad */}
        <div className="bg-gradient-to-br from-purple-600 to-purple-800 rounded-2xl p-8 text-center shadow-lg">
          <div className="text-white">
            <h3 className="text-2xl font-bold mb-2">YOUR</h3>
            <h3 className="text-4xl font-bold mb-4">ADS</h3>
            <p className="text-purple-200 text-sm">120 x 225</p>
          </div>
        </div>

        {/* Second Ad */}
        <div className="bg-gradient-to-br from-purple-600 to-purple-800 rounded-2xl p-8 text-center shadow-lg">
          <div className="text-white">
            <h3 className="text-2xl font-bold mb-2">YOUR</h3>
            <h3 className="text-4xl font-bold mb-4">ADS</h3>
            <p className="text-purple-200 text-sm">120 x 225</p>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default ProfileAdsSidebar;