import React, { useState } from "react";
import { Search, Monitor, User, Home, Target, Heart, Gamepad2, BookOpen } from "lucide-react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

const categories = [
  {
    label: "Electronics",
    icon: Monitor,
  },
  {
    label: "Fashion",
    icon: User,
  },
  {
    label: "Home & Kitchen",
    icon: Home,
  },
  {
    label: "Sports & Outdoors",
    icon: Target,
  },
  {
    label: "Beauty & Health",
    icon: Heart,
  },
  {
    label: "Toys & Games",
    icon: Gamepad2,
  },
  {
    label: "Books",
    icon: BookOpen,
  },
];

const Sidebar: React.FC = () => {
  const [search, setSearch] = useState("");
  const [checked, setChecked] = useState<{ [key: string]: boolean }>({
    Fashion: true,
    "Beauty & Health": true,
  });

  const handleCheckbox = (label: string) => {
    setChecked((prev) => ({ ...prev, [label]: !prev[label] }));
  };

  return (
    <Card className="h-full w-full md:w-80 bg-slate-900 border-slate-800">
      <CardHeader className="pb-0">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-500" />
          <Input
            type="text"
            placeholder="What do you need?"
            className="pl-12 pr-4 py-3 h-12 text-base bg-slate-800 border border-slate-700 text-slate-200 placeholder:text-slate-500 focus:border-blue-500 focus:ring-blue-500/50 rounded-lg"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
      </CardHeader>

      <CardContent className="space-y-2 pt-0 -mt-2">
        <div className="space-y-1">
          {categories.map((cat) => {
            const IconComponent = cat.icon;
            return (
              <div
                key={cat.label}
                onClick={() => handleCheckbox(cat.label)}
                className={cn(
                  "flex items-center justify-between p-3 rounded-lg transition-colors duration-200 cursor-pointer",
                  checked[cat.label]
                    ? "bg-blue-500/20 text-white"
                    : "text-slate-400 hover:bg-slate-800 hover:text-slate-200"
                )}
              >
                <div className="flex items-center gap-3 font-medium flex-1">
                  <IconComponent className="h-5 w-5" />
                  <span>{cat.label}</span>
                </div>
                {checked[cat.label] && (
                  <div className="h-1.5 w-1.5 bg-white rounded-full"></div>
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default Sidebar;