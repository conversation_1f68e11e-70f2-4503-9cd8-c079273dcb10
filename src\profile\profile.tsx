import { useState } from 'react';
import ProfileLayout from './ProfileLayout';

export default function Profile() {
  const [showPhotoMenu, setShowPhotoMenu] = useState(false);
  const [showSellerDialog, setShowSellerDialog] = useState(false);
  const [emailNotifications, setEmailNotifications] = useState(false);

  return (
    <ProfileLayout
      title="Profile"
      subtitle="Manage your account settings"
    >
      <div className="max-w-md mx-auto">
        <div className="bg-gray-800 rounded-xl shadow-sm border border-gray-700 p-8">
          {/* Profile Section */}
          <div className="text-center mb-8">
            <div className="relative inline-block mb-4">
              <div
                onClick={() => setShowPhotoMenu(true)}
                className="w-20 h-20 bg-gray-600 rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-500 transition-colors"
              >
                <svg className="w-8 h-8 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
            </div>
            <h1 className="text-xl font-semibold text-white mb-1">Jokerbro007</h1>
            <p className="text-lg font-medium text-white mb-1">$0.00</p>
            <p className="text-sm text-gray-400">Personal account</p>
          </div>

          {/* Email Section */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-300 mb-2">Email</label>
            <input
              type="email"
              className="w-full px-0 py-2 border-0 border-b border-gray-600 bg-transparent focus:ring-0 focus:border-gray-400 text-white"
              placeholder="Enter your email"
            />
          </div>

          {/* Notifications */}
          <div className="flex items-center justify-between mb-8">
            <span className="text-sm text-gray-300">Send notifications to me via email</span>
            <button
              onClick={() => setEmailNotifications(!emailNotifications)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
                emailNotifications ? 'bg-blue-600' : 'bg-gray-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  emailNotifications ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <div className="flex gap-3">
              <button className="flex-1 px-4 py-2 border border-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                Save
              </button>
              <button
                onClick={() => setShowSellerDialog(true)}
                className="flex-1 px-4 py-2 border border-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Join as a seller
              </button>
            </div>
            <button className="w-full px-4 py-2 border border-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
              Sign out
            </button>
          </div>
        </div>

        {/* Photo Menu Modal */}
        {showPhotoMenu && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-64 shadow-xl">
              <div className="space-y-3">
                <button className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                  New picture
                </button>
                <button className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                  Delete
                </button>
                <hr className="my-2" />
                <button
                  onClick={() => setShowPhotoMenu(false)}
                  className="w-full px-4 py-2 text-left text-gray-500 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Seller Dialog Modal */}
        {showSellerDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-80 shadow-xl">
              <div className="text-center mb-6">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Create your own store</h3>
                <p className="text-gray-600">Start selling for just $100!</p>
              </div>
              <div className="flex gap-3">
                <button className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  Accept
                </button>
                <button
                  onClick={() => setShowSellerDialog(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Decline
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </ProfileLayout>
  );
}
