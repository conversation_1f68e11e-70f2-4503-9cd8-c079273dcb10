import { LayoutGrid, Shirt, Home as HomeIcon, Dumbbell, HeartPulse, Puzzle, Book } from 'lucide-react';
import React from 'react';

interface Category {
  name: string;
  icon: React.ComponentType<{ size: number; className?: string }>;
}

// Mock Data for sidebar categories
export const categories: Category[] = [
  { name: 'Electronics', icon: LayoutGrid },
  { name: 'Fashion', icon: Shirt },
  { name: 'Home & Kitchen', icon: HomeIcon },
  { name: 'Sports & Outdoors', icon: Dumbbell },
  { name: 'Beauty & Health', icon: HeartPulse },
  { name: 'Toys & Games', icon: Puzzle },
  { name: 'Books', icon: Book },
];

export const categoryIconMap: { [key: string]: React.ComponentType<{ size: number; className?: string }> } = {
  Electronics: LayoutGrid,
  Fashion: Shirt,
  'Home & Kitchen': HomeIcon,
  'Sports & Outdoors': Dumbbell,
  'Beauty & Health': HeartPulse,
  'Toys & Games': Puzzle,
  Books: Book,
}; 