import { Link } from 'react-router-dom';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4" style={{ backgroundColor: '#101828' }}>
      <div className="max-w-lg w-full text-center">
        {/* 404 Animation */}
        <div className="relative mb-8">
          <h1 className="text-9xl font-bold text-slate-800 select-none">404</h1>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-6xl animate-bounce">🔍</div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8">
          <h2 className="text-3xl font-bold text-white mb-4">Page Not Found</h2>
          <p className="text-slate-300 mb-8">
            Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
          </p>

          {/* Action Buttons */}
          <div className="space-y-4">
            <Link
              to="/home"
              className="block w-full bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105"
            >
              Go to Home
            </Link>
            
            <button
              onClick={() => window.history.back()}
              className="block w-full bg-slate-600 hover:bg-slate-500 text-white font-bold py-3 px-6 rounded-lg transition-colors"
            >
              Go Back
            </button>
          </div>

          {/* Quick Links */}
          <div className="mt-8 pt-6 border-t border-slate-700">
            <p className="text-slate-400 text-sm mb-4">Quick Links:</p>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <Link
                to="/trusted-seller"
                className="text-slate-300 hover:text-cyan-400 transition-colors"
              >
                Trusted Sellers
              </Link>
              <Link
                to="/auto-escrow"
                className="text-slate-300 hover:text-cyan-400 transition-colors"
              >
                Auto Escrow
              </Link>
              <Link
                to="/news"
                className="text-slate-300 hover:text-cyan-400 transition-colors"
              >
                News
              </Link>
              <Link
                to="/profile"
                className="text-slate-300 hover:text-cyan-400 transition-colors"
              >
                Profile
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
